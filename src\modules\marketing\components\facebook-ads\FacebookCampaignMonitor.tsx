import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Badge,
  Loading,
  Table,
} from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';
import { useRecentFacebookAdsCampaigns } from '../../hooks/facebook-ads/useFacebookAdsCampaigns';

interface CampaignStatus {
  id: string;
  name: string;
  status: 'ACTIVE' | 'PAUSED' | 'DELETED' | 'ARCHIVED';
  objective: string;
  budget: number;
  budgetType: 'daily' | 'lifetime';
  spend: number;
  impressions: number;
  clicks: number;
  ctr: number;
  cpc: number;
  lastUpdated: string;
  alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
  }>;
}

interface FacebookCampaignMonitorProps {
  /**
   * Hiển thị loading state
   */
  isLoading?: boolean;
  
  /**
   * Auto refresh interval (seconds)
   */
  refreshInterval?: number;
  
  /**
   * Callback khi click vào campaign
   */
  onCampaignClick?: (campaignId: string) => void;
  
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Facebook Campaign Monitor Component
 * Theo dõi trạng thái chiến dịch real-time
 */
const FacebookCampaignMonitor: React.FC<FacebookCampaignMonitorProps> = ({
  isLoading = false,
  refreshInterval = 30,
  onCampaignClick,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [lastRefresh, setLastRefresh] = useState(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);

  const {
    isAuthenticated,
  } = useFacebookAuth();

  const {
    data: campaignsData,
    isLoading: campaignsLoading,
    refetch: refetchCampaigns,
  } = useRecentFacebookAdsCampaigns(10);

  // Auto refresh effect
  useEffect(() => {
    if (!autoRefresh || !isAuthenticated) return;

    const interval = setInterval(() => {
      refetchCampaigns();
      setLastRefresh(new Date());
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, isAuthenticated, refetchCampaigns]);

  // Mock campaign status data - In real app, this would come from API
  const campaignStatuses: CampaignStatus[] = React.useMemo(() => {
    const campaigns = campaignsData?.result?.items || [];
    
    return campaigns.map((campaign, index) => ({
      id: campaign.id.toString(),
      name: campaign.name,
      status: campaign.status as CampaignStatus['status'],
      objective: campaign.objective,
      budget: Math.floor(Math.random() * 1000000) + 100000,
      budgetType: Math.random() > 0.5 ? 'daily' : 'lifetime',
      spend: Math.floor(Math.random() * 500000) + 50000,
      impressions: Math.floor(Math.random() * 100000) + 10000,
      clicks: Math.floor(Math.random() * 5000) + 500,
      ctr: Number((Math.random() * 3 + 0.5).toFixed(2)),
      cpc: Math.floor(Math.random() * 5000) + 1000,
      lastUpdated: new Date(Date.now() - Math.random() * 3600000).toISOString(),
      alerts: index % 3 === 0 ? [
        {
          type: 'warning',
          message: t('marketing:facebookAds.monitor.alerts.budgetNearLimit', 'Ngân sách sắp hết'),
        }
      ] : index % 4 === 0 ? [
        {
          type: 'error',
          message: t('marketing:facebookAds.monitor.alerts.lowPerformance', 'Hiệu suất thấp'),
        }
      ] : [],
    }));
  }, [campaignsData, t]);

  // Table columns
  const columns = React.useMemo(() => [
    {
      title: t('marketing:facebookAds.monitor.columns.campaign', 'Chiến dịch'),
      dataIndex: 'name',
      key: 'name',
      render: (value: string, record: CampaignStatus) => (
        <div className="flex items-center space-x-2">
          <div>
            <Typography variant="body2" className="font-medium">
              {value}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground">
              {record.objective}
            </Typography>
          </div>
          {record.alerts.length > 0 && (
            <Icon 
              name="alert-triangle" 
              size="sm" 
              className={record.alerts[0].type === 'error' ? 'text-red-500' : 'text-yellow-500'} 
            />
          )}
        </div>
      ),
    },
    {
      title: t('marketing:facebookAds.monitor.columns.status', 'Trạng thái'),
      dataIndex: 'status',
      key: 'status',
      render: (value: CampaignStatus['status']) => {
        const statusConfig = {
          ACTIVE: { variant: 'success' as const, label: t('common:status.active', 'Hoạt động') },
          PAUSED: { variant: 'warning' as const, label: t('common:status.paused', 'Tạm dừng') },
          DELETED: { variant: 'danger' as const, label: t('common:status.deleted', 'Đã xóa') },
          ARCHIVED: { variant: 'secondary' as const, label: t('common:status.archived', 'Lưu trữ') },
        };
        
        const config = statusConfig[value];
        return <Badge variant={config.variant}>{config.label}</Badge>;
      },
    },
    {
      title: t('marketing:facebookAds.monitor.columns.budget', 'Ngân sách'),
      dataIndex: 'budget',
      key: 'budget',
      render: (value: number, record: CampaignStatus) => (
        <div>
          <Typography variant="body2" className="font-medium">
            {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value)}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {record.budgetType === 'daily' ? t('marketing:facebookAds.budget.daily', 'Hàng ngày') : t('marketing:facebookAds.budget.lifetime', 'Trọn đời')}
          </Typography>
        </div>
      ),
    },
    {
      title: t('marketing:facebookAds.monitor.columns.spend', 'Chi tiêu'),
      dataIndex: 'spend',
      key: 'spend',
      render: (value: number, record: CampaignStatus) => {
        const percentage = (value / record.budget) * 100;
        return (
          <div>
            <Typography variant="body2" className="font-medium">
              {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value)}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground">
              {percentage.toFixed(1)}% {t('marketing:facebookAds.monitor.ofBudget', 'của ngân sách')}
            </Typography>
          </div>
        );
      },
    },
    {
      title: t('marketing:facebookAds.monitor.columns.performance', 'Hiệu suất'),
      key: 'performance',
      render: (record: CampaignStatus) => (
        <div className="space-y-1">
          <div className="flex justify-between">
            <Typography variant="caption" className="text-muted-foreground">CTR:</Typography>
            <Typography variant="caption" className="font-medium">{record.ctr}%</Typography>
          </div>
          <div className="flex justify-between">
            <Typography variant="caption" className="text-muted-foreground">CPC:</Typography>
            <Typography variant="caption" className="font-medium">
              {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(record.cpc)}
            </Typography>
          </div>
          <div className="flex justify-between">
            <Typography variant="caption" className="text-muted-foreground">Clicks:</Typography>
            <Typography variant="caption" className="font-medium">
              {new Intl.NumberFormat('vi-VN').format(record.clicks)}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      title: t('marketing:facebookAds.monitor.columns.lastUpdated', 'Cập nhật'),
      dataIndex: 'lastUpdated',
      key: 'lastUpdated',
      render: (value: string) => (
        <Typography variant="caption" className="text-muted-foreground">
          {new Date(value).toLocaleTimeString('vi-VN')}
        </Typography>
      ),
    },
  ], [t]);

  const dataTable = useDataTable(useDataTableConfig({ 
    columns,
    pageSize: 10,
  }));

  // Manual refresh
  const handleRefresh = () => {
    refetchCampaigns();
    setLastRefresh(new Date());
  };

  // Loading state
  if (isLoading || campaignsLoading) {
    return (
      <div className={className}>
        <Card className="p-6">
          <Loading size="lg" className="flex justify-center py-12" />
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      <Card className="p-4">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-4 sm:space-y-0">
          <div>
            <Typography variant="h6" className="font-bold">
              {t('marketing:facebookAds.monitor.title', 'Theo dõi chiến dịch')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('marketing:facebookAds.monitor.description', 'Theo dõi trạng thái và hiệu suất chiến dịch real-time')}
            </Typography>
          </div>
          
          <div className="flex items-center space-x-2">
            <Typography variant="caption" className="text-muted-foreground">
              {t('marketing:facebookAds.monitor.lastRefresh', 'Cập nhật lần cuối')}: {lastRefresh.toLocaleTimeString('vi-VN')}
            </Typography>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
            >
              <Icon name={autoRefresh ? 'pause' : 'play'} size="sm" className="mr-1" />
              {autoRefresh ? t('common:action.pause', 'Tạm dừng') : t('common:action.start', 'Bắt đầu')}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
            >
              <Icon name="refresh-cw" size="sm" className="mr-1" />
              {t('common:action.refresh', 'Làm mới')}
            </Button>
          </div>
        </div>

        {/* Campaign Table */}
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={campaignStatuses}
          loading={campaignsLoading}
          className="cursor-pointer"
        />
      </Card>
    </div>
  );
};

export default FacebookCampaignMonitor;
