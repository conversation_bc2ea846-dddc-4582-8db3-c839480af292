import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Icon,
  ResponsiveGrid,
  Select,
  Badge,
  Loading,
} from '@/shared/components/common';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';
import { useRecentFacebookAdsCampaigns } from '../../hooks/facebook-ads/useFacebookAdsCampaigns';

interface PerformanceMetric {
  id: string;
  title: string;
  value: string | number;
  change: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: string;
  color: string;
  description?: string;
  format: 'currency' | 'number' | 'percentage';
}

interface FacebookPerformanceDashboardProps {
  /**
   * Hiển thị loading state
   */
  isLoading?: boolean;
  
  /**
   * Khoảng thời gian hiển thị
   */
  timeRange?: '7d' | '30d' | '90d' | 'custom';
  
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Facebook Ads Performance Dashboard Component
 * Dashboard hiệu suất chi tiết với metrics và insights
 */
const FacebookPerformanceDashboard: React.FC<FacebookPerformanceDashboardProps> = ({
  isLoading = false,
  timeRange = '30d',
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [selectedAccount, setSelectedAccount] = useState<string>('all');

  const {
    adAccounts,
    isLoading: authLoading,
  } = useFacebookAuth();

  const {
    isLoading: campaignsLoading,
  } = useRecentFacebookAdsCampaigns(20);

  // Mock performance data - In real app, this would come from API
  const performanceMetrics: PerformanceMetric[] = useMemo(() => [
    {
      id: 'total_spend',
      title: t('marketing:facebookAds.performance.totalSpend', 'Tổng chi tiêu'),
      value: ********,
      change: 12.5,
      changeType: 'increase',
      icon: 'dollar-sign',
      color: 'text-blue-600',
      format: 'currency',
      description: t('marketing:facebookAds.performance.totalSpendDesc', 'Tổng số tiền đã chi tiêu cho quảng cáo'),
    },
    {
      id: 'total_impressions',
      title: t('marketing:facebookAds.performance.totalImpressions', 'Tổng lượt hiển thị'),
      value: 2450000,
      change: 8.3,
      changeType: 'increase',
      icon: 'eye',
      color: 'text-green-600',
      format: 'number',
      description: t('marketing:facebookAds.performance.totalImpressionsDesc', 'Tổng số lần quảng cáo được hiển thị'),
    },
    {
      id: 'total_clicks',
      title: t('marketing:facebookAds.performance.totalClicks', 'Tổng lượt click'),
      value: 48500,
      change: 15.7,
      changeType: 'increase',
      icon: 'mouse-pointer',
      color: 'text-orange-600',
      format: 'number',
      description: t('marketing:facebookAds.performance.totalClicksDesc', 'Tổng số lần người dùng click vào quảng cáo'),
    },
    {
      id: 'avg_ctr',
      title: t('marketing:facebookAds.performance.avgCTR', 'CTR trung bình'),
      value: 1.98,
      change: -2.1,
      changeType: 'decrease',
      icon: 'target',
      color: 'text-purple-600',
      format: 'percentage',
      description: t('marketing:facebookAds.performance.avgCTRDesc', 'Tỷ lệ click trung bình trên tổng số hiển thị'),
    },
    {
      id: 'avg_cpc',
      title: t('marketing:facebookAds.performance.avgCPC', 'CPC trung bình'),
      value: 3200,
      change: -5.4,
      changeType: 'decrease',
      icon: 'credit-card',
      color: 'text-red-600',
      format: 'currency',
      description: t('marketing:facebookAds.performance.avgCPCDesc', 'Chi phí trung bình cho mỗi lượt click'),
    },
    {
      id: 'total_conversions',
      title: t('marketing:facebookAds.performance.totalConversions', 'Tổng chuyển đổi'),
      value: 1250,
      change: 22.8,
      changeType: 'increase',
      icon: 'trending-up',
      color: 'text-emerald-600',
      format: 'number',
      description: t('marketing:facebookAds.performance.totalConversionsDesc', 'Tổng số hành động chuyển đổi'),
    },
  ], [t]);

  // Time range options
  const timeRangeOptions = [
    { value: '7d', label: t('marketing:facebookAds.performance.timeRange.7d', '7 ngày qua') },
    { value: '30d', label: t('marketing:facebookAds.performance.timeRange.30d', '30 ngày qua') },
    { value: '90d', label: t('marketing:facebookAds.performance.timeRange.90d', '90 ngày qua') },
    { value: 'custom', label: t('marketing:facebookAds.performance.timeRange.custom', 'Tùy chỉnh') },
  ];

  // Account options
  const accountOptions = [
    { value: 'all', label: t('marketing:facebookAds.performance.allAccounts', 'Tất cả tài khoản') },
    ...adAccounts.map(account => ({
      value: account.accountId,
      label: account.name,
    })),
  ];

  // Format value based on type
  const formatValue = (value: number, format: PerformanceMetric['format']) => {
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND',
        }).format(value);
      case 'percentage':
        return `${value}%`;
      case 'number':
      default:
        return new Intl.NumberFormat('vi-VN').format(value);
    }
  };

  // Get change icon and color
  const getChangeDisplay = (change: number, changeType: PerformanceMetric['changeType']) => {
    const isPositive = changeType === 'increase';
    const icon = isPositive ? 'trending-up' : changeType === 'decrease' ? 'trending-down' : 'minus';
    const color = isPositive ? 'text-green-600' : changeType === 'decrease' ? 'text-red-600' : 'text-gray-600';
    
    return {
      icon,
      color,
      text: `${isPositive ? '+' : ''}${change}%`,
    };
  };

  // Loading state
  if (isLoading || authLoading || campaignsLoading) {
    return (
      <div className={className}>
        <Card className="p-6">
          <Loading size="lg" className="flex justify-center py-12" />
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Header with filters */}
      <Card className="p-4 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <Typography variant="h6" className="font-bold">
              {t('marketing:facebookAds.performance.title', 'Hiệu suất quảng cáo')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('marketing:facebookAds.performance.description', 'Theo dõi và phân tích hiệu suất chiến dịch Facebook Ads')}
            </Typography>
          </div>
          
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <Select
              value={selectedAccount}
              onChange={(value) => setSelectedAccount(value)}
              options={accountOptions}
              placeholder={t('marketing:facebookAds.performance.selectAccount', 'Chọn tài khoản')}
              className="min-w-[200px]"
            />
            <Select
              value={selectedTimeRange}
              onChange={(value) => setSelectedTimeRange(value as typeof selectedTimeRange)}
              options={timeRangeOptions}
              placeholder={t('marketing:facebookAds.performance.selectTimeRange', 'Chọn khoảng thời gian')}
              className="min-w-[150px]"
            />
          </div>
        </div>
      </Card>

      {/* Performance Metrics Grid */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3 }}>
        {performanceMetrics.map((metric) => {
          const changeDisplay = getChangeDisplay(metric.change, metric.changeType);
          
          return (
            <Card key={metric.id} className="p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <Icon name={metric.icon as 'dollar-sign' | 'eye' | 'mouse-pointer' | 'target' | 'credit-card' | 'trending-up'} size="sm" className={metric.color} />
                    <Typography variant="body2" className="text-muted-foreground">
                      {metric.title}
                    </Typography>
                  </div>
                  
                  <Typography variant="h4" className="font-bold mb-1">
                    {formatValue(metric.value as number, metric.format)}
                  </Typography>
                  
                  <div className="flex items-center space-x-1">
                    <Icon name={changeDisplay.icon as 'trending-up' | 'trending-down' | 'minus'} size="xs" className={changeDisplay.color} />
                    <Typography variant="caption" className={changeDisplay.color}>
                      {changeDisplay.text}
                    </Typography>
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('marketing:facebookAds.performance.vsLastPeriod', 'so với kỳ trước')}
                    </Typography>
                  </div>
                </div>
                
                <Badge
                  variant={metric.changeType === 'increase' ? 'success' : metric.changeType === 'decrease' ? 'destructive' : 'secondary'}
                  className="ml-2"
                >
                  {metric.changeType === 'increase' ? '↗' : metric.changeType === 'decrease' ? '↘' : '→'}
                </Badge>
              </div>
              
              {metric.description && (
                <Typography variant="caption" className="text-muted-foreground mt-2 block">
                  {metric.description}
                </Typography>
              )}
            </Card>
          );
        })}
      </ResponsiveGrid>
    </div>
  );
};

export default FacebookPerformanceDashboard;
